'use client';

import { <PERSON><PERSON>, But<PERSON>, Translate } from '@/components/ui';
import { ExamplesDialog, WordNetDialog } from '@/components';
import { WordNetSummary } from '@/components/wordnet';
import { cn, getTranslationKeyOfLanguage } from '@/lib';
import { RandomWord, WordDetail, WordNetData } from '@/models';
import { Language } from '@prisma/client';
import { Volume2 } from 'lucide-react';
import { memo, useCallback, useState } from 'react';
import { useSpeechSynthesis } from '@/hooks/use-speech-synthesis';

interface WordDetailsProps {
	word: RandomWord;
	details: WordDetail | undefined;
	sourceLanguage: Language;
	targetLanguage: Language;
	onGenerateExamples?: (word: RandomWord) => Promise<void>;
	generatingExamples?: boolean;
	onSearchWordNetTerm?: (term: string) => void;
	wordNetSearchLoading?: Record<string, boolean>;
	className?: string;
}

function WordDetailsComponent({
	word,
	details,
	sourceLanguage,
	targetLanguage,
	onGenerateExamples,
	generatingExamples = false,
	onSearchWordNetTerm,
	wordNetSearchLoading = {},
	className = '',
}: WordDetailsProps) {
	const { speak, isSupported } = useSpeechSynthesis();
	const [examplesDialogOpen, setExamplesDialogOpen] = useState(false);
	const [wordNetDialogOpen, setWordNetDialogOpen] = useState(false);

	const handlePlayAudio = useCallback(
		(text: string, language: Language) => {
			if (isSupported) {
				speak(text, language);
			}
		},
		[speak, isSupported]
	);
	return (
		<div className={cn('space-y-4', className)}>
			{/* Header with term and part of speech */}
			<div className="flex-1">
				<div className="flex items-center gap-3 mb-2">
					<h3 className="text-3xl font-bold tracking-tight text-primary drop-shadow-sm">
						{word.term}
					</h3>
					{isSupported && (
						<Button
							variant="ghost"
							size="sm"
							onClick={() => handlePlayAudio(word.term, sourceLanguage)}
							className="h-8 w-8 p-0 hover:bg-primary/10 transition-colors"
							title={`Pronounce "${word.term}"`}
						>
							<Volume2 className="h-4 w-4 text-primary" />
						</Button>
					)}
				</div>
				<div className="flex flex-wrap items-center gap-2 mb-2">
					{word.partOfSpeech && word.partOfSpeech.length > 0 && (
						<div className="flex flex-wrap gap-1">
							{word.partOfSpeech.map((pos, index) => (
								<Badge
									key={index}
									variant="secondary"
									className="text-xs font-medium"
								>
									{pos}
								</Badge>
							))}
						</div>
					)}
					{/* WordNet Summary for detailed words */}
					{details?.WordNetData && <WordNetSummary wordNetData={details.WordNetData} />}
				</div>
			</div>

			{/* Basic meaning from RandomWord (when no details) */}
			{!details && word.meaning && word.meaning.length > 0 && (
				<div className="space-y-2">
					{word.meaning.map((meaning, index) => (
						<div
							key={index}
							className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
						>
							<div className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1">
								<p className="text-xs font-medium text-muted-foreground tracking-wide">
									<Translate text={getTranslationKeyOfLanguage(targetLanguage)} />
									:
								</p>
								<div className="flex items-start gap-2 mb-1">
									<p className="flex-1 text-sm text-foreground/95">
										{meaning[targetLanguage] || (
											<span className="italic opacity-70">
												<Translate text="words.meaning_not_provided" />
											</span>
										)}
									</p>
									{isSupported && meaning[targetLanguage] && (
										<Button
											variant="ghost"
											size="sm"
											onClick={() =>
												handlePlayAudio(
													meaning[targetLanguage],
													targetLanguage
												)
											}
											className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
											title={`Pronounce meaning in ${targetLanguage}`}
										>
											<Volume2 className="h-3 w-3 text-primary" />
										</Button>
									)}
								</div>
								<p className="text-xs font-medium text-muted-foreground tracking-wide">
									<Translate text={getTranslationKeyOfLanguage(sourceLanguage)} />
									:
								</p>
								<div className="flex items-start gap-2">
									<p className="flex-1 text-sm text-foreground/95">
										{meaning[sourceLanguage] || (
											<span className="italic opacity-70">
												<Translate text="words.translation_not_provided" />
											</span>
										)}
									</p>
									{isSupported && meaning[sourceLanguage] && (
										<Button
											variant="ghost"
											size="sm"
											onClick={() =>
												handlePlayAudio(
													meaning[sourceLanguage],
													sourceLanguage
												)
											}
											className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
											title={`Pronounce meaning in ${sourceLanguage}`}
										>
											<Volume2 className="h-3 w-3 text-primary" />
										</Button>
									)}
								</div>
							</div>
						</div>
					))}
				</div>
			)}

			{/* Detailed information from WordDetail */}
			{details && details.definitions && details.definitions.length > 0
				? details.definitions.map((definition, index) => (
						<div
							key={index}
							className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
						>
							{definition.pos && definition.pos.length > 0 && (
								<p className="text-xs font-semibold uppercase tracking-wider text-primary/90 mb-2.5">
									{definition.pos.join(', ')}
								</p>
							)}
							{definition.ipa && (
								<div className="flex items-center gap-2 mb-2.5">
									<p className="text-sm text-muted-foreground italic">
										IPA: {definition.ipa}
									</p>
									{isSupported && (
										<Button
											variant="ghost"
											size="sm"
											onClick={() =>
												handlePlayAudio(word.term, sourceLanguage)
											}
											className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors"
											title={`Pronounce IPA: ${definition.ipa}`}
										>
											<Volume2 className="h-3 w-3 text-primary" />
										</Button>
									)}
								</div>
							)}
							{/* Explanations section */}
							{definition.explains && definition.explains.length > 0 ? (
								<div className="mb-3">
									<p className="text-sm font-semibold text-muted-foreground mb-1.5">
										<Translate text="words.explanations" />:
									</p>
									{definition.explains.map((explain, expIndex) => (
										<div
											key={expIndex}
											className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1"
										>
											<p className="text-xs font-medium text-muted-foreground tracking-wide">
												<Translate
													text={getTranslationKeyOfLanguage(
														targetLanguage
													)}
												/>
												:
											</p>
											<div className="flex items-start gap-2 mb-1">
												<p className="flex-1 text-sm text-foreground/95">
													{explain[targetLanguage] || (
														<span className="italic opacity-70">
															<Translate text="words.explanation_not_provided" />
														</span>
													)}
												</p>
												{isSupported && explain[targetLanguage] && (
													<Button
														variant="ghost"
														size="sm"
														onClick={() =>
															handlePlayAudio(
																explain[targetLanguage],
																targetLanguage
															)
														}
														className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
														title={`Pronounce explanation in ${targetLanguage}`}
													>
														<Volume2 className="h-3 w-3 text-primary" />
													</Button>
												)}
											</div>
											<p className="text-xs font-medium text-muted-foreground tracking-wide">
												<Translate
													text={getTranslationKeyOfLanguage(
														sourceLanguage
													)}
												/>
												:
											</p>
											<div className="flex items-start gap-2">
												<p className="flex-1 text-sm text-foreground/95">
													{explain[sourceLanguage] || (
														<span className="italic opacity-70">
															<Translate text="words.translation_not_provided" />
														</span>
													)}
												</p>
												{isSupported && explain[sourceLanguage] && (
													<Button
														variant="ghost"
														size="sm"
														onClick={() =>
															handlePlayAudio(
																explain[sourceLanguage],
																sourceLanguage
															)
														}
														className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
														title={`Pronounce explanation in ${sourceLanguage}`}
													>
														<Volume2 className="h-3 w-3 text-primary" />
													</Button>
												)}
											</div>
										</div>
									))}
								</div>
							) : (
								<div className="mb-3">
									<p className="text-sm font-semibold text-muted-foreground mb-1.5">
										<Translate text="words.explanations" />:
									</p>
									<p className="p-2 text-sm text-muted-foreground italic opacity-70">
										<Translate text="words.no_explanations_provided" />
									</p>
								</div>
							)}

							{/* Examples section - Show only first example */}
							{details?.id ? (
								definition.examples && definition.examples.length > 0 ? (
									<div>
										<div className="flex items-center justify-between mb-1.5">
											<p className="text-sm font-semibold text-muted-foreground">
												<Translate text="words.examples" />:
											</p>
											{definition.examples.length > 1 && (
												<Button
													variant="ghost"
													size="sm"
													onClick={() => setExamplesDialogOpen(true)}
													className="text-xs text-primary hover:text-primary/80"
												>
													<Translate text="words.view_all_examples" />
												</Button>
											)}
										</div>
										{/* Show only the first example */}
										{definition.examples.slice(0, 1).map((example, exIndex) => (
											<div
												key={exIndex}
												className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
											>
												<p className="text-xs font-medium text-muted-foreground tracking-wide">
													<Translate
														text={getTranslationKeyOfLanguage(
															targetLanguage
														)}
													/>
													:
												</p>
												<div className="flex items-start gap-2 mb-1">
													<p className="flex-1 text-sm text-foreground/95">
														{example[targetLanguage] || (
															<span className="italic opacity-70">
																<Translate text="words.example_not_provided" />
															</span>
														)}
													</p>
													{isSupported && example[targetLanguage] && (
														<Button
															variant="ghost"
															size="sm"
															onClick={() =>
																handlePlayAudio(
																	example[targetLanguage],
																	targetLanguage
																)
															}
															className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
															title={`Pronounce example in ${targetLanguage}`}
														>
															<Volume2 className="h-3 w-3 text-primary" />
														</Button>
													)}
												</div>
												<p className="text-xs font-medium text-muted-foreground tracking-wide">
													<Translate
														text={getTranslationKeyOfLanguage(
															sourceLanguage
														)}
													/>
													:
												</p>
												<div className="flex items-start gap-2">
													<p className="flex-1 text-sm text-foreground/95">
														{example[sourceLanguage] || (
															<span className="italic opacity-70">
																<Translate text="words.translation_not_provided" />
															</span>
														)}
													</p>
													{isSupported && example[sourceLanguage] && (
														<Button
															variant="ghost"
															size="sm"
															onClick={() =>
																handlePlayAudio(
																	example[sourceLanguage],
																	sourceLanguage
																)
															}
															className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
															title={`Pronounce example in ${sourceLanguage}`}
														>
															<Volume2 className="h-3 w-3 text-primary" />
														</Button>
													)}
												</div>
											</div>
										))}
									</div>
								) : (
									<div>
										<p className="text-sm font-semibold text-muted-foreground mb-1.5">
											<Translate text="words.examples" />:
										</p>
										<p className="p-2 text-sm text-muted-foreground italic opacity-70">
											<Translate text="words.no_examples_provided" />
										</p>
									</div>
								)
							) : (
								<div>
									<div className="flex items-center justify-between mb-1.5">
										<p className="text-sm font-semibold text-muted-foreground">
											<Translate text="words.examples" />:
										</p>
									</div>
									<p className="text-sm text-muted-foreground italic">
										<Translate text="words.save_word_to_load_examples" />
									</p>
								</div>
							)}
						</div>
				  ))
				: details && (
						<p className="p-4 text-sm text-muted-foreground italic">
							<Translate text="words.no_definitions_available" />
						</p>
				  )}

			{/* WordNet Information Button */}
			{(details?.WordNetData || word.wordnet_data) && (
				<div className="mt-4">
					<Button
						variant="outline"
						size="sm"
						onClick={() => setWordNetDialogOpen(true)}
						className="w-full flex items-center justify-center gap-2"
					>
						<Translate text="words.view_wordnet_info" />
					</Button>
				</div>
			)}

			{/* Examples Dialog */}
			{details && details.definitions && details.definitions.length > 0 && (
				<ExamplesDialog
					open={examplesDialogOpen}
					onOpenChange={setExamplesDialogOpen}
					wordId={details.id}
					definition={details.definitions[0]} // Use first definition for examples
					sourceLanguage={sourceLanguage}
					targetLanguage={targetLanguage}
					wordTerm={word.term}
				/>
			)}

			{/* WordNet Dialog */}
			{(details?.WordNetData || word.wordnet_data) && (
				<WordNetDialog
					open={wordNetDialogOpen}
					onOpenChange={setWordNetDialogOpen}
					wordNetData={(details?.WordNetData || word.wordnet_data) as WordNetData}
					wordTerm={word.term}
					onSearchTerm={onSearchWordNetTerm}
				/>
			)}
		</div>
	);
}

const arePropsEqual = (prevProps: WordDetailsProps, nextProps: WordDetailsProps) => {
	return (
		prevProps.word.term === nextProps.word.term &&
		prevProps.details?.id === nextProps.details?.id &&
		prevProps.sourceLanguage === nextProps.sourceLanguage &&
		prevProps.targetLanguage === nextProps.targetLanguage &&
		prevProps.generatingExamples === nextProps.generatingExamples &&
		prevProps.className === nextProps.className &&
		prevProps.onGenerateExamples === nextProps.onGenerateExamples &&
		prevProps.onSearchWordNetTerm === nextProps.onSearchWordNetTerm &&
		prevProps.wordNetSearchLoading === nextProps.wordNetSearchLoading
	);
};

export const WordDetails = memo(WordDetailsComponent, arePropsEqual);
