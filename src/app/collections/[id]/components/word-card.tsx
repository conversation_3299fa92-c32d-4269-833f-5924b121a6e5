'use client';

import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Translate,
} from '@/components/ui';
import { ExamplesDialog } from '@/components/examples-dialog';
import { WordNetDialog } from '@/components/wordnet-dialog';
import { WordNetSummary } from '@/components/wordnet';
import { cn, getTranslationKeyOfLanguage } from '@/lib';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { ChevronDown, ChevronUp, Eye, EyeOff, PlusCircle, Trash2, Volume2 } from 'lucide-react';
import { memo, useCallback, useState } from 'react';
import { useSpeechSynthesis } from '@/hooks/use-speech-synthesis';

interface WordCardProps {
	word: WordDetail;
	onAddToCollection?: () => void;
	isAddingToCollection?: boolean;
	onDeleteWord?: () => void;
	isDeleting?: boolean;
	className?: string;
	isReviewMode?: bool<PERSON>; // Added for review mode
	showSourceLanguage?: boolean; // Added to control target language text visibility in review mode
	onToggleTargetLanguage?: () => void; // Added to toggle target language text visibility
	defaultExpanded?: boolean; // Added to control initial expanded state
	sourceLanguage: Language; // Source language from collection
	targetLanguage: Language; // Target language from collection
	selectionMode?: boolean; // Added to control transitions in selection mode
	onSearchWordNetTerm?: (term: string) => void; // Added for WordNet search functionality
}

function WordCardComponent({
	word,
	onAddToCollection,
	isAddingToCollection,
	onDeleteWord,
	isDeleting,
	className,
	isReviewMode = false, // Default to false
	showSourceLanguage = true, // Default to true (relevant only in review mode)
	onToggleTargetLanguage,
	defaultExpanded, // Accepted defaultExpanded prop
	sourceLanguage, // Default fallback
	targetLanguage, // Default fallback
	selectionMode = false, // Default to false
	onSearchWordNetTerm, // Added for WordNet search functionality
}: WordCardProps) {
	// Use defaultExpanded prop for initial state, fallback to false
	// In review mode, default to collapsed for better UX
	const [isExpanded, setIsExpanded] = useState(isReviewMode ? false : defaultExpanded ?? false);
	const [examplesDialogOpen, setExamplesDialogOpen] = useState(false);
	const [wordNetDialogOpen, setWordNetDialogOpen] = useState(false);
	const { speak, isSupported } = useSpeechSynthesis();

	const handlePlayAudio = useCallback(
		(text: string, language: Language) => {
			if (isSupported) {
				speak(text, language);
			}
		},
		[speak, isSupported]
	);

	const handleToggleExpand = useCallback(() => {
		setIsExpanded((prev) => !prev);
	}, []);

	const handleAddToCollection = useCallback(() => {
		if (onAddToCollection) {
			onAddToCollection();
		}
	}, [onAddToCollection]);

	const handleDeleteWord = useCallback(() => {
		if (onDeleteWord) {
			onDeleteWord();
		}
	}, [onDeleteWord]);
	return (
		<Card
			className={cn(
				'relative flex flex-col break-inside-avoid shadow-lg border border-border bg-background word-card',
				!selectionMode && 'hover:shadow-xl transition-all duration-200',
				className
			)}
			data-expanded={isExpanded}
		>
			<CardHeader className="py-4 px-5 border-b border-border bg-gradient-to-r from-primary/5 via-primary/10 to-transparent rounded-t-lg">
				<CardTitle className="flex justify-between items-start gap-3">
					<div className="flex-grow">
						<span className="text-2xl md:text-3xl font-bold tracking-tight text-primary drop-shadow-sm">
							{word.term}
						</span>
						{/* WordNet Summary */}
						<div className="mt-2">
							<WordNetSummary wordNetData={word.WordNetData} />
						</div>
					</div>
					<div className="flex flex-col items-end space-y-1.5 flex-shrink-0">
						<Button
							variant="ghost"
							size="icon"
							onClick={handleToggleExpand}
							aria-label={isExpanded ? 'Collapse' : 'Expand'}
						>
							{isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
						</Button>
					</div>
				</CardTitle>
			</CardHeader>
			{isExpanded && (
				<CardContent className="flex-grow flex flex-col p-5">
					<div className="space-y-4 flex-grow">
						{word.definitions && word.definitions.length > 0 ? (
							word.definitions.map((definition, index) => (
								<div
									key={index}
									className={cn(
										'p-4 rounded-xl border border-border/70 bg-accent/25 dark:bg-accent/15',
										!selectionMode &&
											'hover:bg-accent/40 dark:hover:bg-accent/30 transition-colors duration-150'
									)}
								>
									{definition.pos && definition.pos.length > 0 && (
										<p className="text-xs font-semibold uppercase tracking-wider text-primary/90 mb-2.5">
											{definition.pos.join(', ')}
										</p>
									)}
									{definition.ipa && (
										<p className="text-sm text-muted-foreground italic mb-2.5">
											IPA: {definition.ipa}
										</p>
									)}
									{/* Explanations section */}
									{definition.explains && definition.explains.length > 0 ? (
										<div className="mb-3">
											{definition.explains.map((explain, expIndex) => (
												<div
													key={expIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																targetLanguage
															)}
														/>
														:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{explain[targetLanguage] || (
															<span className="italic opacity-70">
																<Translate text="words.explanation_not_provided" />
															</span>
														)}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																sourceLanguage
															)}
														/>
														:
													</p>
													{(!isReviewMode || showSourceLanguage) && (
														<p className="text-sm text-foreground/95">
															{explain[sourceLanguage] || (
																<span className="italic opacity-70">
																	<Translate text="words.translation_not_provided" />
																</span>
															)}
														</p>
													)}
													{isReviewMode && !showSourceLanguage && (
														<p className="text-sm text-muted-foreground italic">
															<Translate text="collections.hidden" />
														</p>
													)}
												</div>
											))}
										</div>
									) : (
										<div className="mb-3">
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												<Translate text="words.explanations" />:
											</p>
											<p className="p-2 text-sm text-muted-foreground italic opacity-70">
												<Translate text="words.no_explanations_provided" />
											</p>
										</div>
									)}

									{/* Examples section - Show only first example */}
									{definition.examples && definition.examples.length > 0 ? (
										<div>
											<div className="flex items-center justify-between mb-1.5">
												<p className="text-sm font-semibold text-muted-foreground">
													<Translate text="words.examples" />:
												</p>
												{definition.examples.length > 1 && (
													<Button
														variant="ghost"
														size="sm"
														onClick={() => setExamplesDialogOpen(true)}
														className="text-xs text-primary hover:text-primary/80"
													>
														<Translate text="words.view_all_examples" />
													</Button>
												)}
											</div>
											{/* Show only the first example */}
											{definition.examples
												.slice(0, 1)
												.map((example, exIndex) => (
													<div
														key={exIndex}
														className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
													>
														<p className="text-xs font-medium text-muted-foreground tracking-wide">
															<Translate
																text={getTranslationKeyOfLanguage(
																	targetLanguage
																)}
															/>
															:
														</p>
														<div className="flex items-start gap-2 mb-1">
															<p className="flex-1 text-sm text-foreground/95">
																{example[targetLanguage] || (
																	<span className="italic opacity-70">
																		<Translate text="words.example_not_provided" />
																	</span>
																)}
															</p>
															{isSupported &&
																example[targetLanguage] && (
																	<Button
																		variant="ghost"
																		size="sm"
																		onClick={() =>
																			handlePlayAudio(
																				example[
																					targetLanguage
																				],
																				targetLanguage
																			)
																		}
																		className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
																		title={`Pronounce example in ${targetLanguage}`}
																	>
																		<Volume2 className="h-3 w-3 text-primary" />
																	</Button>
																)}
														</div>
														<p className="text-xs font-medium text-muted-foreground tracking-wide">
															<Translate
																text={getTranslationKeyOfLanguage(
																	sourceLanguage
																)}
															/>
															:
														</p>
														{(!isReviewMode || showSourceLanguage) && (
															<div className="flex items-start gap-2">
																<p className="flex-1 text-sm text-foreground/95">
																	{example[sourceLanguage] || (
																		<span className="italic opacity-70">
																			<Translate text="words.translation_not_provided" />
																		</span>
																	)}
																</p>
																{isSupported &&
																	example[sourceLanguage] && (
																		<Button
																			variant="ghost"
																			size="sm"
																			onClick={() =>
																				handlePlayAudio(
																					example[
																						sourceLanguage
																					],
																					sourceLanguage
																				)
																			}
																			className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
																			title={`Pronounce example in ${sourceLanguage}`}
																		>
																			<Volume2 className="h-3 w-3 text-primary" />
																		</Button>
																	)}
															</div>
														)}
														{isReviewMode && !showSourceLanguage && (
															<p className="text-sm text-muted-foreground italic">
																<Translate text="collections.hidden" />
															</p>
														)}
													</div>
												))}
										</div>
									) : (
										<div>
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												<Translate text="collections.examples" />
											</p>
											<p className="p-2 text-sm text-muted-foreground italic opacity-70">
												<Translate text="words.no_examples_provided" />
											</p>
										</div>
									)}
								</div>
							))
						) : (
							<p className="p-4 text-sm text-muted-foreground italic">
								<Translate text="words.no_definitions_available" />
							</p>
						)}
					</div>

					{/* WordNet Information Button */}
					{word.WordNetData && (
						<div className="mt-4">
							<Button
								variant="outline"
								size="sm"
								onClick={() => setWordNetDialogOpen(true)}
								className="w-full flex items-center justify-center gap-2"
							>
								<Translate text="words.view_wordnet_info" />
							</Button>
						</div>
					)}

					{/* Action buttons */}
					<div className="mt-auto flex flex-col space-y-2 pt-5">
						{isReviewMode && onToggleTargetLanguage && (
							<Button
								onClick={onToggleTargetLanguage}
								variant="outline"
								size="sm"
								className="w-full flex items-center justify-center gap-2"
							>
								{showSourceLanguage ? (
									<>
										<EyeOff size={16} />
										<span>
											<Translate text="words.hide" />{' '}
											<Translate
												text={getTranslationKeyOfLanguage(sourceLanguage)}
											/>
										</span>
									</>
								) : (
									<>
										<Eye size={16} />
										<span>
											<Translate text="words.show" />{' '}
											<Translate
												text={getTranslationKeyOfLanguage(sourceLanguage)}
											/>
										</span>
									</>
								)}
							</Button>
						)}
						{!isReviewMode && onAddToCollection && (
							<Button
								onClick={handleAddToCollection}
								disabled={isAddingToCollection}
								size="sm"
								className="w-full flex items-center justify-center gap-2"
							>
								{isAddingToCollection ? (
									<LoadingSpinner size="sm" />
								) : (
									<PlusCircle size={16} />
								)}
								<span>
									{isAddingToCollection ? (
										<Translate text="collections.adding_to_collection" />
									) : (
										<Translate text="collections.add_to_collection" />
									)}
								</span>
							</Button>
						)}
						{!isReviewMode && onDeleteWord && (
							<Button
								onClick={handleDeleteWord}
								disabled={isDeleting}
								variant="outline"
								size="sm"
								className={cn(
									'w-full flex items-center justify-center gap-2 text-destructive',
									!selectionMode &&
										'transition-colors duration-150 hover:bg-destructive/5 dark:hover:bg-destructive/10 hover:text-destructive/90'
								)}
							>
								{isDeleting ? <LoadingSpinner size="sm" /> : <Trash2 size={16} />}
								<span>
									{isDeleting ? (
										<Translate text="collections.deleting_word" />
									) : (
										<Translate text="collections.delete_word" />
									)}
								</span>
							</Button>
						)}
					</div>
				</CardContent>
			)}{' '}
			{/* Close isExpanded conditional */}
			{/* Examples Dialog */}
			{word.definitions && word.definitions.length > 0 && (
				<ExamplesDialog
					open={examplesDialogOpen}
					onOpenChange={setExamplesDialogOpen}
					wordId={word.id}
					definition={word.definitions[0]} // Use first definition for examples
					sourceLanguage={sourceLanguage}
					targetLanguage={targetLanguage}
					wordTerm={word.term}
				/>
			)}
			{/* WordNet Dialog */}
			{word.WordNetData && (
				<WordNetDialog
					open={wordNetDialogOpen}
					onOpenChange={setWordNetDialogOpen}
					wordNetData={word.WordNetData}
					wordTerm={word.term}
					onSearchTerm={onSearchWordNetTerm}
				/>
			)}
		</Card>
	);
}

const arePropsEqual = (prevProps: WordCardProps, nextProps: WordCardProps) => {
	return (
		prevProps.word.id === nextProps.word.id &&
		prevProps.isAddingToCollection === nextProps.isAddingToCollection &&
		prevProps.isDeleting === nextProps.isDeleting &&
		prevProps.className === nextProps.className &&
		prevProps.isReviewMode === nextProps.isReviewMode && // Added for review mode
		prevProps.showSourceLanguage === nextProps.showSourceLanguage && // Added for review mode
		prevProps.defaultExpanded === nextProps.defaultExpanded && // Added for comparison
		prevProps.sourceLanguage === nextProps.sourceLanguage &&
		prevProps.targetLanguage === nextProps.targetLanguage &&
		// Shallow compare functions assuming they are stable (e.g., from useCallback with stable deps)
		prevProps.onAddToCollection === nextProps.onAddToCollection &&
		prevProps.onDeleteWord === nextProps.onDeleteWord &&
		prevProps.onToggleTargetLanguage === nextProps.onToggleTargetLanguage // Added for review mode
	);
};

export const WordCard = memo(WordCardComponent, arePropsEqual);
