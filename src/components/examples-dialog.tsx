'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	Di<PERSON>Header,
	DialogTitle,
	Translate,
} from '@/components/ui';
import { useSpeechSynthesis } from '@/hooks/use-speech-synthesis';
import { useWordExamples } from '@/hooks/use-word-examples';
import { getTranslationKeyOfLanguage } from '@/lib';
import { Language } from '@prisma/client';
import { ChevronDown, Loader2, Volume2, Languages } from 'lucide-react';
import React, { memo, useCallback, useEffect, useState } from 'react';

interface Example {
	id?: string;
	EN: string;
	VI: string;
	created_at?: Date;
	updated_at?: Date;
}

interface Definition {
	id: string;
	examples: Example[];
}

interface ExamplesDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	wordId: string;
	definition: Definition;
	sourceLanguage: Language;
	targetLanguage: Language;
	wordTerm: string; // For dialog title
}

function ExamplesDialogComponent({
	open,
	onOpenChange,
	wordId,
	definition,
	sourceLanguage,
	targetLanguage,
	wordTerm,
}: ExamplesDialogProps) {
	const { getExampleState, loadMoreExamples, initializeExamples } = useWordExamples();
	const { speak, isSupported } = useSpeechSynthesis();
	const [showTargetLanguage, setShowTargetLanguage] = useState(true);

	const handlePlayAudio = useCallback(
		(text: string, language: Language) => {
			if (isSupported) {
				speak(text, language);
			}
		},
		[speak, isSupported]
	);

	const exampleState = getExampleState(definition.id);

	// Initialize examples state when dialog opens or definition changes
	React.useEffect(() => {
		if (open && definition.examples.length > 0) {
			initializeExamples(definition.id, definition.examples);
		}
	}, [open, definition.id, definition.examples, initializeExamples]);

	// Combine initial examples with loaded examples, avoiding duplicates and maintaining chronological order
	const allExamples = React.useMemo(() => {
		const seenIds = new Set<string>();
		const seenContents = new Set<string>();
		const combined = [];

		// Helper function to normalize content for comparison
		const normalizeContent = (example: Example) =>
			`${example.EN.toLowerCase().trim()}|${example.VI.toLowerCase().trim()}`;

		// Add initial examples first
		for (const example of definition.examples) {
			const normalizedContent = normalizeContent(example);
			if (example.id && !seenIds.has(example.id)) {
				seenIds.add(example.id);
				seenContents.add(normalizedContent);
				combined.push(example);
			} else if (!example.id && !seenContents.has(normalizedContent)) {
				seenContents.add(normalizedContent);
				combined.push(example);
			}
		}

		// Add loaded examples that aren't duplicates
		for (const example of exampleState.examples) {
			const normalizedContent = normalizeContent(example);
			if (example.id && !seenIds.has(example.id)) {
				seenIds.add(example.id);
				seenContents.add(normalizedContent);
				combined.push(example);
			} else if (!example.id && !seenContents.has(normalizedContent)) {
				seenContents.add(normalizedContent);
				combined.push(example);
			}
		}

		return combined;
	}, [definition.examples, exampleState.examples]);

	const totalExamplesShown = allExamples.length;
	const isLoadingExamples = exampleState.loading;

	// Use server-provided hasMore flag instead of frontend calculation
	const canLoadMore =
		wordId && // Only show if we have a valid wordId
		exampleState.hasMore && // Use server hasMore
		!isLoadingExamples;

	const handleLoadMore = async () => {
		if (!wordId) return; // Safety check
		console.log(
			`[ExamplesDialog] Load more clicked for ${definition.id}, current examples: ${allExamples.length}`
		);
		await loadMoreExamples(wordId, definition.id);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
				<DialogHeader>
					<div className="flex items-center justify-between">
						<DialogTitle className="flex items-center gap-2">
							<Translate text="words.examples" />
							<span className="text-primary font-bold">"{wordTerm}"</span>
						</DialogTitle>

						{/* Language toggle button */}
						<Button
							variant="outline"
							size="sm"
							onClick={() => setShowTargetLanguage(!showTargetLanguage)}
							className="flex items-center gap-2 text-xs"
						>
							<Languages className="h-3 w-3" />
							{showTargetLanguage ? (
								<Translate text={getTranslationKeyOfLanguage(targetLanguage)} />
							) : (
								<Translate text={getTranslationKeyOfLanguage(sourceLanguage)} />
							)}
						</Button>
					</div>
				</DialogHeader>

				<div className="flex-1 overflow-y-auto space-y-4 pr-2">
					{allExamples.length > 0 ? (
						<>
							{allExamples.map((example, exIndex) => {
								const displayLanguage = showTargetLanguage
									? targetLanguage
									: sourceLanguage;

								return (
									<div
										key={
											example.id ||
											`example-${exIndex}-${example.EN?.slice(
												0,
												20
											)}-${example.VI?.slice(0, 20)}`
										}
										className="pl-3 border-l-2 border-primary/30 py-2 space-y-2 bg-accent/20 rounded-r-lg pr-3"
									>
										{/* Display selected language */}
										<div>
											<div className="flex items-start gap-2">
												<p className="flex-1 text-sm text-foreground/95 font-medium">
													{example[displayLanguage] || (
														<span className="italic opacity-70">
															<Translate text="words.translation_not_provided" />
														</span>
													)}
												</p>
												{isSupported && example[displayLanguage] && (
													<Button
														variant="ghost"
														size="sm"
														onClick={() =>
															handlePlayAudio(
																example[displayLanguage],
																displayLanguage
															)
														}
														className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
														title={`Pronounce example in ${displayLanguage}`}
													>
														<Volume2 className="h-3 w-3 text-primary" />
													</Button>
												)}
											</div>
										</div>
									</div>
								);
							})}

							{/* Load More Button */}
							{canLoadMore && (
								<div className="flex justify-center pt-2">
									<Button
										variant="outline"
										onClick={handleLoadMore}
										disabled={isLoadingExamples}
										className="flex items-center gap-2"
									>
										{isLoadingExamples ? (
											<>
												<Loader2 className="h-4 w-4 animate-spin" />
												<Translate text="words.loading_examples" />
											</>
										) : (
											<>
												<ChevronDown className="h-4 w-4" />
												<Translate text="words.load_more_examples" />
											</>
										)}
									</Button>
								</div>
							)}
						</>
					) : (
						<div className="text-center py-8">
							<p className="text-muted-foreground">
								<Translate text="words.no_examples_available" />
							</p>
						</div>
					)}

					{/* Loading Skeleton for new examples */}
					{isLoadingExamples && (
						<div className="space-y-4 mt-4">
							{[1, 2, 3].map((i) => (
								<div key={`skeleton-${i}`} className="animate-pulse">
									<div className="pl-3 border-l-2 border-secondary/30 py-2 space-y-2 bg-accent/20 rounded-r-lg pr-3">
										<div className="h-3 bg-muted-foreground/20 rounded mb-1 w-12"></div>
										<div className="h-4 bg-muted-foreground/20 rounded mb-2 w-full"></div>
										<div className="h-3 bg-muted-foreground/20 rounded mb-1 w-12"></div>
										<div className="h-4 bg-muted-foreground/20 rounded w-3/4"></div>
									</div>
								</div>
							))}
						</div>
					)}

					{/* Error Message */}
					{exampleState.error && (
						<div className="text-center py-4">
							<p className="text-sm text-destructive">{exampleState.error}</p>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

export const ExamplesDialog = memo(ExamplesDialogComponent);
